// Gemini AI 搜索助手
class GeminiSearchAssistant {
    constructor(config) {
        this.config = config.gemini || {};
        this.enabled = config.search?.enableAI ?? false;
        this.fallbackEnabled = config.search?.fallbackToTraditional ?? true;
    }
    
    // 检查是否可用
    isAvailable() {
        return this.enabled && this.config.apiKey && !this.config.apiKey.includes('XXXXX');
    }
    
    // 增强搜索查询
    async enhanceSearchQuery(query, language = 'zh') {
        if (!this.isAvailable()) {
            console.debug('Gemini assistant not available');
            return { enhanced: false, query: query };
        }
        
        try {
            const prompt = this.buildEnhancePrompt(query, language);
            console.debug('Gemini prompt:', prompt.substring(0, 100) + '...');
            
            if (this.config.enableStream) {
                console.debug('Using stream API');
                const response = await this.callGeminiStreamAPI(prompt);
                return response;
            } else {
                console.debug('Using standard API');
                const response = await this.callGeminiAPI(prompt);
                
                if (response && response.keywords) {
                    console.debug('Gemini enhancement successful:', response);
                    return {
                        enhanced: true,
                        originalQuery: query,
                        enhancedQuery: response.keywords.join(' '),
                        keywords: response.keywords,
                        intent: response.intent,
                        suggestions: response.suggestions || []
                    };
                }
            }
        } catch (error) {
            // 静默处理错误，不在控制台显示警告
            console.debug('Gemini search enhancement failed:', error.message);
        }
        
        return { enhanced: false, query: query };
    }
    
    // 智能搜索建议
    async getSearchSuggestions(query, availableQuestions, language = 'zh') {
        if (!this.isAvailable()) {
            return [];
        }
        
        try {
            const prompt = this.buildSuggestionPrompt(query, availableQuestions, language);
            const response = await this.callGeminiAPI(prompt);
            
            if (response && response.suggestions) {
                return response.suggestions;
            }
        } catch (error) {
            console.warn('Gemini suggestions failed:', error);
        }
        
        return [];
    }
    
    // 构建增强搜索的提示词
    buildEnhancePrompt(query, language) {
        const langMap = {
            'zh': '中文',
            'en': '英文', 
            'ms': '马来文'
        };
        
        return `你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："${query}"，语言是${langMap[language]}。

请分析这个查询并提供：
1. 相关的关键词（包括同义词、相关术语）
2. 用户的搜索意图
3. 可能的搜索建议

请以JSON格式回复：
{
    "keywords": ["关键词1", "关键词2", "关键词3"],
    "intent": "用户搜索意图描述",
    "suggestions": ["建议1", "建议2", "建议3"]
}

专注于司机相关的术语，如：注册、APP使用、订单、支付、评价、车辆、安全等主题。`;
    }
    
    // 构建建议提示词
    buildSuggestionPrompt(query, availableQuestions, language) {
        const questionSample = availableQuestions.slice(0, 20).map(q => 
            `- ${q.id}: ${q.title[language]}`
        ).join('\n');
        
        return `用户搜索："${query}"

以下是部分可用的FAQ问题：
${questionSample}

请基于用户查询和可用问题，推荐最相关的3-5个问题ID，以JSON数组格式回复：
["Q001", "Q002", "Q003"]

只返回问题ID，确保ID在提供的列表中存在。`;
    }
    
    // 调用Gemini API
    async callGeminiAPI(prompt) {
        const url = `${this.config.endpoint}${this.config.model}:generateContent?key=${this.config.apiKey}`;
        
        const requestBody = {
            contents: [{
                parts: [{
                    text: prompt
                }]
            }],
            generationConfig: {
                temperature: this.config.temperature,
                maxOutputTokens: this.config.maxTokens,
                candidateCount: 1
            }
        };
        
        console.debug('Calling Gemini API:', url);
        
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });
        
        if (!response.ok) {
            console.debug('Gemini API error response:', response.status, response.statusText);
            throw new Error(`Gemini API error: ${response.status}`);
        }
        
        const data = await response.json();
        console.debug('Gemini API response:', data);
        
        if (data.candidates && data.candidates[0] && data.candidates[0].content) {
            const text = data.candidates[0].content.parts[0].text;
            console.debug('Gemini response text:', text);
            try {
                const result = JSON.parse(text);
                console.debug('Parsed JSON result:', result);
                return result;
            } catch (e) {
                console.debug('Failed to parse as JSON, trying to extract JSON part');
                // 如果不是JSON，尝试提取JSON部分
                const jsonMatch = text.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    const result = JSON.parse(jsonMatch[0]);
                    console.debug('Extracted JSON result:', result);
                    return result;
                }
                throw new Error('Invalid JSON response from Gemini');
            }
        }
        
        throw new Error('No valid response from Gemini');
    }
    
    // 调用Gemini流式API
    async callGeminiStreamAPI(prompt) {
        const url = `${this.config.streamEndpoint}${this.config.model}:streamGenerateContent?key=${this.config.apiKey}`;
        
        const requestBody = {
            contents: [{
                parts: [{
                    text: prompt
                }]
            }],
            generationConfig: {
                temperature: this.config.temperature,
                maxOutputTokens: this.config.maxTokens,
                candidateCount: 1
            }
        };
        
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });
        
        if (!response.ok) {
            throw new Error(`Gemini Stream API error: ${response.status}`);
        }
        
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let fullResponse = '';
        let buffer = '';
        
        try {
            while (true) {
                const { done, value } = await reader.read();
                
                if (done) break;
                
                const chunk = decoder.decode(value, { stream: true });
                buffer += chunk;
                
                // 处理流式响应中的多个JSON对象
                const lines = buffer.split('\n');
                buffer = lines.pop() || ''; // 保留最后一个不完整的行
                
                for (const line of lines) {
                    const trimmedLine = line.trim();
                    if (trimmedLine) {
                        try {
                            let jsonStr = trimmedLine;
                            
                            // 移除常见的流式前缀
                            if (jsonStr.startsWith('data: ')) {
                                jsonStr = jsonStr.substring(6);
                            }
                            
                            // 跳过结束标记
                            if (jsonStr === '[DONE]' || jsonStr === 'data: [DONE]') {
                                continue;
                            }
                            
                            // 尝试解析 JSON
                            const data = JSON.parse(jsonStr);
                            
                            // 提取文本内容
                            let text = '';
                            if (data.candidates && data.candidates[0] && data.candidates[0].content) {
                                text = data.candidates[0].content.parts[0].text;
                            } else if (data.text) {
                                text = data.text;
                            } else if (data.message) {
                                text = data.message;
                            }
                            
                            if (text) {
                                fullResponse += text;
                                // 触发流式更新事件（如果需要实时显示）
                                this.onStreamUpdate && this.onStreamUpdate(text);
                            }
                        } catch (e) {
                            // 如果不是JSON，可能是纯文本响应
                            if (trimmedLine && !trimmedLine.startsWith('{') && !trimmedLine.startsWith('data:')) {
                                fullResponse += trimmedLine + ' ';
                            }
                            console.debug('Failed to parse stream chunk:', trimmedLine, e.message);
                        }
                    }
                }
            }
            
            // 解析完整响应
            if (fullResponse.trim()) {
                try {
                    // 首先尝试直接解析为JSON
                    const result = JSON.parse(fullResponse);
                    if (result && result.keywords) {
                        return {
                            enhanced: true,
                            originalQuery: prompt.match(/："(.+?)"/)?.[1] || '',
                            enhancedQuery: result.keywords.join(' '),
                            keywords: result.keywords,
                            intent: result.intent,
                            suggestions: result.suggestions || []
                        };
                    }
                } catch (e) {
                    // 如果不是纯JSON，尝试提取JSON部分
                    try {
                        const jsonMatch = fullResponse.match(/\{[\s\S]*\}/);
                        if (jsonMatch) {
                            const result = JSON.parse(jsonMatch[0]);
                            if (result && result.keywords) {
                                return {
                                    enhanced: true,
                                    originalQuery: prompt.match(/："(.+?)"/)?.[1] || '',
                                    enhancedQuery: result.keywords.join(' '),
                                    keywords: result.keywords,
                                    intent: result.intent,
                                    suggestions: result.suggestions || []
                                };
                            }
                        }
                    } catch (e2) {
                        // 最后尝试从文本中提取关键词
                        const keywordMatch = fullResponse.match(/关键词.*?[:：]\s*(.+)/);
                        if (keywordMatch) {
                            const keywords = keywordMatch[1].split(/[,，\s]+/).filter(k => k.trim());
                            if (keywords.length > 0) {
                                return {
                                    enhanced: true,
                                    originalQuery: prompt.match(/："(.+?)"/)?.[1] || '',
                                    enhancedQuery: keywords.join(' '),
                                    keywords: keywords,
                                    intent: '搜索',
                                    suggestions: []
                                };
                            }
                        }
                    }
                }
            }
            
        } finally {
            reader.releaseLock();
        }
        
        // 如果流式API失败，尝试使用标准API作为备用
        console.debug('Stream API failed, trying standard API as fallback');
        try {
            const fallbackResponse = await this.callGeminiAPI(prompt);
            if (fallbackResponse && fallbackResponse.keywords) {
                return {
                    enhanced: true,
                    originalQuery: prompt.match(/："(.+?)"/)?.[1] || '',
                    enhancedQuery: fallbackResponse.keywords.join(' '),
                    keywords: fallbackResponse.keywords,
                    intent: fallbackResponse.intent,
                    suggestions: fallbackResponse.suggestions || []
                };
            }
        } catch (fallbackError) {
            console.debug('Fallback API also failed:', fallbackError.message);
        }
        
        // 如果所有方法都失败，返回未增强的结果
        return { enhanced: false, query: prompt.match(/："(.+?)"/)?.[1] || '' };
    }
    
    // 测试API连接
    async testAPIConnection() {
        try {
            const testPrompt = '请简单回复"连接成功"';
            const url = `${this.config.endpoint}${this.config.model}:generateContent?key=${this.config.apiKey}`;
            const requestBody = {
                contents: [{ parts: [{ text: testPrompt }] }],
                generationConfig: { temperature: 0.1, maxOutputTokens: 50 }
            };

            const response = await fetch(url, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                throw new Error(`API request failed with status ${response.status}`);
            }

            const data = await response.json();
            
            if (data.candidates && data.candidates[0] && data.candidates[0].content) {
                const text = data.candidates[0].content.parts[0].text;
                if (text.includes('连接成功')) {
                    console.debug('API连接测试成功:', text);
                    return true;
                }
            }
            throw new Error('API response did not contain "连接成功"');

        } catch (error) {
            console.error('API连接测试失败:', error.message);
            return false;
        }
    }
    
    // 设置流式更新回调
    onStreamUpdate(callback) {
        this.onStreamUpdate = callback;
    }
    
    // 设置启用状态
    setEnabled(enabled) {
        this.enabled = enabled;
    }
    
    // 获取状态
    getStatus() {
        return {
            enabled: this.enabled,
            available: this.isAvailable(),
            model: this.config.model
        };
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GeminiSearchAssistant;
} else {
    window.GeminiSearchAssistant = GeminiSearchAssistant;
}
