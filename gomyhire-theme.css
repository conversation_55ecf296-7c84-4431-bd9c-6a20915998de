/* GoMyHire 紫色主题专用样式 */

/* 紫色主题渐变背景 */
.purple-gradient-bg {
    background: linear-gradient(135deg, #d946ef 0%, #b83dba 50%, #a855f7 100%);
}

.purple-light-bg {
    background: linear-gradient(135deg, #f3e8f4 0%, #faf5ff 100%);
}

/* GoMyHire Logo 文字样式 */
.logo-text h1 {
    background: linear-gradient(135deg, #d946ef 0%, #b83dba 50%, #a855f7 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
    letter-spacing: -0.5px;
}

/* 紫色主题按钮悬停效果 */
.btn-primary:hover,
.btn-brand:hover {
    background: linear-gradient(135deg, #c026d3 0%, #9c2a9e 50%, #9333ea 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(184, 61, 186, 0.4);
}

/* 紫色主题卡片 */
.purple-card {
    background: var(--surface-color);
    border: 1px solid rgba(184, 61, 186, 0.1);
    border-radius: var(--border-radius-lg);
    box-shadow: 0 4px 20px rgba(184, 61, 186, 0.08);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.purple-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #d946ef, #b83dba, #a855f7);
    transform: scaleX(0);
    transition: var(--transition);
}

.purple-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 30px rgba(184, 61, 186, 0.15);
    border-color: rgba(184, 61, 186, 0.2);
}

.purple-card:hover::before {
    transform: scaleX(1);
}

/* 紫色主题搜索框 */
.search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(184, 61, 186, 0.1);
}

.search-btn {
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
}

.search-btn:hover {
    background: linear-gradient(135deg, var(--secondary-hover), var(--primary-hover));
}

/* 紫色主题分类导航 */
.category-link:hover,
.category-link.active {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
}

/* 紫色主题标签 */
.purple-badge {
    background: linear-gradient(135deg, rgba(217, 70, 239, 0.1), rgba(168, 85, 247, 0.1));
    color: var(--primary-color);
    border: 1px solid rgba(184, 61, 186, 0.2);
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 0.75rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

/* 紫色主题进度条 */
.purple-progress {
    width: 100%;
    height: 6px;
    background: rgba(184, 61, 186, 0.1);
    border-radius: 3px;
    overflow: hidden;
}

.purple-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #d946ef, #b83dba, #a855f7);
    border-radius: 3px;
    transition: width 0.3s ease;
    position: relative;
}

.purple-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: purple-shine 2s ease-in-out infinite;
}

@keyframes purple-shine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* 紫色主题通知 */
.purple-notification {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    padding: 12px 20px;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 20px rgba(184, 61, 186, 0.3);
    border: none;
}

/* 紫色主题加载动画 */
.purple-loader {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(184, 61, 186, 0.1);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: purple-spin 1s linear infinite;
}

@keyframes purple-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 紫色主题脉冲效果 */
.purple-pulse {
    animation: purple-pulse 2s ease-in-out infinite;
}

@keyframes purple-pulse {
    0%, 100% { 
        box-shadow: 0 0 0 0 rgba(184, 61, 186, 0.4);
    }
    50% { 
        box-shadow: 0 0 0 10px rgba(184, 61, 186, 0);
    }
}

/* 紫色主题悬浮效果 */
.purple-float {
    animation: purple-float 3s ease-in-out infinite;
}

@keyframes purple-float {
    0%, 100% { 
        transform: translateY(0px);
        box-shadow: 0 4px 20px rgba(184, 61, 186, 0.1);
    }
    50% { 
        transform: translateY(-10px);
        box-shadow: 0 8px 30px rgba(184, 61, 186, 0.2);
    }
}

/* 紫色主题闪光效果 */
.purple-shimmer {
    position: relative;
    overflow: hidden;
}

.purple-shimmer::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(217, 70, 239, 0.1), transparent);
    transform: rotate(45deg);
    animation: purple-shimmer 3s ease-in-out infinite;
}

@keyframes purple-shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    50% { transform: translateX(50%) translateY(50%) rotate(45deg); }
    100% { transform: translateX(200%) translateY(200%) rotate(45deg); }
}

/* 紫色主题状态指示器 */
.purple-status {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    background: rgba(184, 61, 186, 0.1);
    color: var(--primary-color);
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.purple-status::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--primary-color);
    animation: purple-pulse 2s ease-in-out infinite;
}

/* 紫色主题工具提示 */
.purple-tooltip::after {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
}

.purple-tooltip::before {
    border-top-color: var(--primary-color);
}

/* 紫色主题分割线 */
.purple-divider {
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(184, 61, 186, 0.3), transparent);
    margin: 24px 0;
}

.purple-divider.thick {
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
}

/* 紫色主题阴影 */
.purple-shadow-sm {
    box-shadow: 0 2px 8px rgba(184, 61, 186, 0.1);
}

.purple-shadow-md {
    box-shadow: 0 4px 16px rgba(184, 61, 186, 0.15);
}

.purple-shadow-lg {
    box-shadow: 0 8px 32px rgba(184, 61, 186, 0.2);
}

/* 紫色主题文本渐变 */
.purple-text-gradient {
    background: linear-gradient(135deg, #d946ef, #b83dba, #a855f7);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
}

/* 紫色主题边框 */
.purple-border {
    border: 1px solid rgba(184, 61, 186, 0.2);
}

.purple-border-gradient {
    border: 2px solid;
    border-image: linear-gradient(135deg, #d946ef, #b83dba, #a855f7) 1;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .purple-card {
        margin-bottom: 16px;
    }
    
    .purple-badge {
        font-size: 0.7rem;
        padding: 3px 8px;
    }
}
