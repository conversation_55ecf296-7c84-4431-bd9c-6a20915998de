/* 手机端FAQ+对话式设计 - 合并版CSS */

:root {
  /* 品牌颜色系统 */
  --primary-color: #d946ef;
  --primary-dark: #a855f7;
  --primary-light: rgba(217, 70, 239, 0.1);
  --secondary-color: #b83dba;
  --accent-color: #9333ea;
  
  /* 语义颜色 */
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --background-color: #ffffff;
  --surface-color: #ffffff;
  --border-color: #e5e7eb;
  --border-light: #f3f4f6;
  --danger-color: #ef4444;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  
  /* 手机端尺寸系统 */
  --header-height: 56px;
  --bottom-nav-height: 60px;
  --fab-size: 56px;
  --emergency-size: 48px;
  --touch-target: 48px;
  
  /* 安全区域 */
  --safe-top: env(safe-area-inset-top);
  --safe-bottom: env(safe-area-inset-bottom);
  --safe-left: env(safe-area-inset-left);
  --safe-right: env(safe-area-inset-right);
  
  /* 间距系统 */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 12px;
  --space-lg: 16px;
  --space-xl: 20px;
  --space-2xl: 24px;
  
  /* 圆角系统 */
  --radius-sm: 4px;
  --radius: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-full: 50%;
  
  /* 字体系统 */
  --font-xs: 12px;
  --font-sm: 14px;
  --font-base: 16px;
  --font-lg: 18px;
  --font-xl: 20px;
  --font-2xl: 24px;
  
  /* 阴影系统 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15);
  
  /* 过渡动画 */
  --transition-fast: 0.15s ease;
  --transition: 0.3s ease;
}

/* 全局重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: var(--font-base);
  line-height: 1.5;
  color: var(--text-primary);
  background: var(--background-color);
  -webkit-font-smoothing: antialiased;
  -webkit-tap-highlight-color: transparent;
  overflow-x: hidden;
  padding-top: var(--safe-top);
  padding-bottom: calc(var(--bottom-nav-height) + var(--safe-bottom));
}

/* 手机端头部 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: var(--header-height);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  display: flex;
  align-items: center;
  padding: 0 var(--space-lg);
  z-index: 1000;
  box-shadow: var(--shadow);
}

.header-content {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  width: 100%;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.gmh-logo-img {
  height: 32px;
  width: auto;
}

.logo-text h1 {
  font-size: var(--font-lg);
  font-weight: 700;
  white-space: nowrap;
}

/* 搜索栏 */
.search-container {
  flex: 1;
  position: relative;
}

.search-input-wrapper {
  position: relative;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  padding: 0 var(--space-sm);
}

.search-input {
  flex: 1;
  background: none;
  border: none;
  color: white;
  font-size: var(--font-base);
  padding: var(--space-sm) 0;
  outline: none;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.gemini-toggle {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius);
  font-size: var(--font-xs);
  cursor: pointer;
}

/* 手机端底部导航 */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: var(--bottom-nav-height);
  background: white;
  display: flex;
  box-shadow: 0 -4px 6px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 2px;
  color: var(--text-secondary);
  font-size: var(--font-xs);
  cursor: pointer;
  transition: var(--transition-fast);
  text-decoration: none;
}

.nav-item.active {
  color: var(--primary-color);
}

.nav-item .icon {
  font-size: var(--font-lg);
}

/* 浮动对话按钮 */
.fab-chat {
  position: fixed;
  bottom: calc(var(--bottom-nav-height) + var(--space-lg));
  right: var(--space-lg);
  width: var(--fab-size);
  height: var(--fab-size);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border: none;
  border-radius: var(--radius-full);
  color: white;
  font-size: var(--font-xl);
  box-shadow: var(--shadow-lg);
  cursor: pointer;
  z-index: 1001;
  transition: var(--transition);
}

.fab-chat:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-xl);
}

/* 紧急联系按钮 */
.emergency-btn {
  position: fixed;
  bottom: calc(var(--bottom-nav-height) + var(--fab-size) + var(--space-2xl));
  right: var(--space-lg);
  width: var(--emergency-size);
  height: var(--emergency-size);
  background: var(--danger-color);
  border: none;
  border-radius: var(--radius-full);
  color: white;
  font-size: var(--font-lg);
  box-shadow: var(--shadow-lg);
  cursor: pointer;
  z-index: 1001;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* 主内容区域 */
.main-content {
  margin-top: var(--header-height);
  padding: var(--space-lg);
  max-width: 100%;
  overflow-x: hidden;
}

/* FAQ卡片 */
.faq-card {
  background: white;
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  margin-bottom: var(--space-md);
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
}

.faq-card:hover {
  box-shadow: var(--shadow);
  transform: translateY(-1px);
}

.faq-header {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  margin-bottom: var(--space-md);
}

.faq-id {
  background: var(--primary-color);
  color: white;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius);
  font-size: var(--font-xs);
  font-weight: 600;
  min-width: 48px;
  text-align: center;
}

.faq-title {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.3;
}

.faq-summary {
  color: var(--text-secondary);
  line-height: 1.5;
  margin-bottom: var(--space-sm);
}

.faq-meta {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  font-size: var(--font-xs);
  color: var(--text-secondary);
}

/* 对话界面 */
.chat-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: white;
  z-index: 2000;
  display: none;
  flex-direction: column;
}

.chat-container.active {
  display: flex;
}

.chat-header {
  background: var(--primary-color);
  color: white;
  padding: var(--space-lg);
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.chat-back {
  background: none;
  border: none;
  color: white;
  font-size: var(--font-xl);
  cursor: pointer;
}

.chat-messages {
  flex: 1;
  padding: var(--space-lg);
  overflow-y: auto;
}

.chat-input-container {
  padding: var(--space-lg);
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: var(--space-sm);
}

.chat-input {
  flex: 1;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--space-sm) var(--space-md);
  font-size: var(--font-base);
  outline: none;
}

.chat-send {
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius);
  padding: var(--space-sm) var(--space-lg);
  cursor: pointer;
}

/* 响应式调整 */
@media (max-width: 375px) {
  :root {
    --space-lg: 12px;
    --font-base: 14px;
  }
  
  .header {
    padding: 0 var(--space-md);
  }
  
  .logo-text h1 {
    font-size: var(--font-base);
  }
  
  .fab-chat {
    bottom: calc(var(--bottom-nav-height) + var(--space-md));
    right: var(--space-md);
  }
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --background-color: #111827;
    --surface-color: #1f2937;
    --border-color: #374151;
  }
}

/* 流畅动画 */
.faq-card {
  animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}