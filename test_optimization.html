<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GoMyHire FAQ系统优化测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .faq-image {
            margin: 10px 0;
            text-align: center;
        }
        .responsive-image {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .image-caption {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <h1>GoMyHire FAQ系统优化测试</h1>
    
    <div class="test-section">
        <h2>1. 系统初始化测试</h2>
        <button onclick="testInitialization()">测试初始化</button>
        <div id="init-result" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 分类索引测试</h2>
        <button onclick="testCategoryIndex()">测试分类索引</button>
        <div id="category-result" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 搜索功能测试</h2>
        <input type="text" id="search-input" placeholder="输入搜索关键词" value="登录问题">
        <button onclick="testSearch()">测试搜索</button>
        <div id="search-result" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 图片集成测试</h2>
        <button onclick="testImages()">测试图片功能</button>
        <div id="image-result" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>5. 图片验证测试</h2>
        <button onclick="testImageValidation()">验证图片完整性</button>
        <div id="validation-result" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>6. 分类统计测试</h2>
        <button onclick="testCategoryStats()">获取分类统计</button>
        <div id="stats-result" class="test-result"></div>
    </div>

    <script src="data.js"></script>
    <script>
        let enhancedManager;
        
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `test-result ${type}`;
        }
        
        function testInitialization() {
            try {
                // 测试基础数据管理器
                const basicManager = new DataManager();
                log('init-result', `✓ 基础DataManager初始化成功\n问题总数: ${basicManager.data.questions.length}`, 'success');
                
                // 测试增强数据管理器
                enhancedManager = new EnhancedDataManager();
                enhancedManager.initialize();
                log('init-result', `✓ 增强DataManager初始化成功\n问题总数: ${enhancedManager.data.questions.length}\n分类索引: ${Object.keys(enhancedManager.categoryMappings).length}个分类`, 'success');
                
            } catch (error) {
                log('init-result', `✗ 初始化失败: ${error.message}`, 'error');
            }
        }
        
        function testCategoryIndex() {
            try {
                if (!enhancedManager) {
                    enhancedManager = new EnhancedDataManager();
                    enhancedManager.initialize();
                }
                
                const categories = Object.keys(faqCategoryIndex.primary);
                const result = categories.map(cat => {
                    const info = faqCategoryIndex.primary[cat];
                    return `${cat}: ${info.zh} (${info.en})`;
                }).join('\n');
                
                log('category-result', `✓ 分类索引测试成功\n主要分类 (${categories.length}个):\n${result}`, 'success');
                
            } catch (error) {
                log('category-result', `✗ 分类索引测试失败: ${error.message}`, 'error');
            }
        }
        
        async function testSearch() {
            try {
                if (!enhancedManager) {
                    enhancedManager = new EnhancedDataManager();
                    enhancedManager.initialize();
                }
                
                const query = document.getElementById('search-input').value;
                const results = await enhancedManager.smartSearch(query, 'zh');
                
                if (results.success) {
                    const resultText = results.results.slice(0, 3).map(r => 
                        `${r.question.id}: ${r.question.title.zh} (分数: ${r.score.toFixed(2)})`
                    ).join('\n');
                    
                    log('search-result', `✓ 搜索测试成功\n查询: "${query}"\n找到 ${results.totalResults} 个结果\n前3个结果:\n${resultText}`, 'success');
                } else {
                    log('search-result', `✗ 搜索失败: ${results.error}`, 'error');
                }
                
            } catch (error) {
                log('search-result', `✗ 搜索测试失败: ${error.message}`, 'error');
            }
        }
        
        function testImages() {
            try {
                if (!enhancedManager) {
                    enhancedManager = new EnhancedDataManager();
                    enhancedManager.initialize();
                }
                
                // 测试图片获取
                const images = enhancedManager.getQuestionImages('FC-CT-01', 'zh');
                const imageManifest = enhancedManager.getImageManifest();
                
                let result = `✓ 图片功能测试成功\n`;
                result += `FC-CT-01的图片数量: ${images.length}\n`;
                result += `总图片占位符: ${imageManifest.totalImages}\n`;
                result += `图片清单示例:\n`;
                
                images.slice(0, 2).forEach(img => {
                    result += `- ${img.id}: ${img.description}\n`;
                });
                
                // 测试图片HTML生成
                const sampleHtml = imageAssets.getImageHtml('faq-ct-01-step1', 'zh', '测试图片');
                result += `\n图片HTML生成测试:\n${sampleHtml}`;
                
                log('image-result', result, 'success');
                
            } catch (error) {
                log('image-result', `✗ 图片测试失败: ${error.message}`, 'error');
            }
        }
        
        function testImageValidation() {
            try {
                if (!enhancedManager) {
                    enhancedManager = new EnhancedDataManager();
                    enhancedManager.initialize();
                }
                
                const validation = enhancedManager.validateImages();
                
                let result = `图片验证结果:\n`;
                result += `总图片数: ${validation.total}\n`;
                result += `有效图片: ${validation.valid.length}\n`;
                result += `缺失图片: ${validation.missing.length}\n`;
                result += `完整性: ${validation.completeness}%\n`;
                
                if (validation.missing.length > 0) {
                    result += `\n缺失的图片:\n${validation.missing.slice(0, 5).join('\n')}`;
                    if (validation.missing.length > 5) {
                        result += `\n... 还有 ${validation.missing.length - 5} 个`;
                    }
                }
                
                const type = validation.completeness > 80 ? 'success' : 
                           validation.completeness > 50 ? 'warning' : 'error';
                
                log('validation-result', result, type);
                
            } catch (error) {
                log('validation-result', `✗ 图片验证失败: ${error.message}`, 'error');
            }
        }
        
        function testCategoryStats() {
            try {
                if (!enhancedManager) {
                    enhancedManager = new EnhancedDataManager();
                    enhancedManager.initialize();
                }
                
                const stats = enhancedManager.getCategoryStats();
                
                let result = `✓ 分类统计测试成功\n`;
                Object.keys(stats).forEach(category => {
                    const categoryInfo = faqCategoryIndex.primary[category];
                    const categoryName = categoryInfo ? categoryInfo.zh : category;
                    result += `${categoryName} (${category}): ${stats[category].count} 个问题\n`;
                });
                
                log('stats-result', result, 'success');
                
            } catch (error) {
                log('stats-result', `✗ 分类统计测试失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时自动运行初始化测试
        window.onload = function() {
            testInitialization();
        };
    </script>
</body>
</html>
